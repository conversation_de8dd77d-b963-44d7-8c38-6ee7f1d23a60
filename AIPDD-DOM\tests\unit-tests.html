<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIPDD-DOM 单元测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .test-suite { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 4px; }
        .suite-header { background: #f8f9fa; padding: 15px; font-weight: bold; border-bottom: 1px solid #ddd; }
        .test-case { padding: 10px 15px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
        .test-case:last-child { border-bottom: none; }
        .test-case.passed { background: #d4edda; }
        .test-case.failed { background: #f8d7da; }
        .test-case.pending { background: #fff3cd; }
        .status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .status.passed { background: #28a745; color: white; }
        .status.failed { background: #dc3545; color: white; }
        .status.pending { background: #ffc107; color: #212529; }
        .error-details { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; font-family: monospace; font-size: 12px; color: #dc3545; }
        .controls { margin-bottom: 20px; text-align: center; }
        .btn { padding: 10px 20px; margin: 0 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .summary { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 AIPDD-DOM 单元测试</h1>
            <p>基于重构进度的模块功能测试</p>
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="runAllTests()">运行所有测试</button>
            <button class="btn btn-success" onclick="runCoreTests()">运行Core模块测试</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>
        
        <div id="test-results"></div>
        
        <div class="summary" id="summary" style="display: none;">
            <strong>测试总结:</strong>
            <span id="summary-text"></span>
        </div>
    </div>

    <!-- 模拟Chrome扩展环境 -->
    <script>
        // 模拟chrome API
        window.chrome = {
            runtime: {
                getURL: function(path) { return path; },
                sendMessage: function(message, callback) { 
                    setTimeout(() => callback && callback({success: true}), 100); 
                }
            },
            storage: {
                local: {
                    get: function(keys, callback) {
                        const mockData = {
                            transferSettings: { manualEnabled: true, keywordEnabled: true },
                            transferKeywords: ['转人工', '转接', '人工客服'],
                            serviceAccounts: ['客服001', '客服002']
                        };
                        const result = {};
                        if (Array.isArray(keys)) {
                            keys.forEach(key => result[key] = mockData[key]);
                        } else if (typeof keys === 'string') {
                            result[keys] = mockData[keys];
                        } else {
                            Object.assign(result, mockData);
                        }
                        setTimeout(() => callback(result), 10);
                    },
                    set: function(data, callback) {
                        setTimeout(() => callback && callback(), 10);
                    }
                }
            }
        };
        
        // 模拟DOM元素
        function createMockElement(tag, attributes = {}) {
            const element = document.createElement(tag);
            Object.assign(element, attributes);
            return element;
        }
    </script>

    <!-- 加载Core模块 -->
    <script src="../src/core/utils/debug.js"></script>
    <script src="../src/core/utils/dom-utils.js"></script>
    <script src="../src/core/settings/settings-manager.js"></script>
    <script src="../src/core/storage/storage-manager.js"></script>
    <script src="../src/core/transfer/transfer-rules.js"></script>
    <script src="../src/core/transfer/transfer-manager.js"></script>
    <script src="../src/core/ui/dialog.js"></script>

    <!-- 测试框架和用例 -->
    <script>
        // 简单的测试框架
        class TestFramework {
            constructor() {
                this.suites = [];
                this.currentSuite = null;
                this.results = { total: 0, passed: 0, failed: 0, pending: 0 };
            }
            
            describe(name, fn) {
                this.currentSuite = { name, tests: [] };
                this.suites.push(this.currentSuite);
                fn();
                this.currentSuite = null;
            }
            
            it(description, fn) {
                if (!this.currentSuite) throw new Error('Test must be inside describe block');
                this.currentSuite.tests.push({ description, fn, status: 'pending' });
            }
            
            async runTests() {
                this.results = { total: 0, passed: 0, failed: 0, pending: 0 };
                const container = document.getElementById('test-results');
                container.innerHTML = '';
                
                for (const suite of this.suites) {
                    const suiteDiv = this.createSuiteElement(suite);
                    container.appendChild(suiteDiv);
                    
                    for (const test of suite.tests) {
                        this.results.total++;
                        try {
                            await test.fn();
                            test.status = 'passed';
                            this.results.passed++;
                        } catch (error) {
                            test.status = 'failed';
                            test.error = error.message;
                            this.results.failed++;
                        }
                        this.updateTestElement(test);
                    }
                }
                
                this.showSummary();
            }
            
            createSuiteElement(suite) {
                const suiteDiv = document.createElement('div');
                suiteDiv.className = 'test-suite';
                suiteDiv.innerHTML = `
                    <div class="suite-header">${suite.name}</div>
                    <div class="suite-content" id="suite-${suite.name.replace(/\s+/g, '-')}"></div>
                `;
                
                const content = suiteDiv.querySelector('.suite-content');
                suite.tests.forEach(test => {
                    const testDiv = document.createElement('div');
                    testDiv.className = 'test-case pending';
                    testDiv.innerHTML = `
                        <span>${test.description}</span>
                        <span class="status pending">待测试</span>
                    `;
                    test.element = testDiv;
                    content.appendChild(testDiv);
                });
                
                return suiteDiv;
            }
            
            updateTestElement(test) {
                const element = test.element;
                element.className = `test-case ${test.status}`;
                const statusElement = element.querySelector('.status');
                statusElement.className = `status ${test.status}`;
                statusElement.textContent = test.status === 'passed' ? '✓ 通过' : 
                                          test.status === 'failed' ? '✗ 失败' : '待测试';
                
                if (test.error) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'error-details';
                    errorDiv.textContent = test.error;
                    element.appendChild(errorDiv);
                }
            }
            
            showSummary() {
                const summary = document.getElementById('summary');
                const summaryText = document.getElementById('summary-text');
                summaryText.textContent = `总计 ${this.results.total} 个测试，通过 ${this.results.passed} 个，失败 ${this.results.failed} 个`;
                summary.style.display = 'block';
            }
        }
        
        const test = new TestFramework();
        
        // 断言函数
        function expect(actual) {
            return {
                toBe: (expected) => {
                    if (actual !== expected) {
                        throw new Error(`Expected ${expected}, but got ${actual}`);
                    }
                },
                toEqual: (expected) => {
                    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                        throw new Error(`Expected ${JSON.stringify(expected)}, but got ${JSON.stringify(actual)}`);
                    }
                },
                toBeTruthy: () => {
                    if (!actual) {
                        throw new Error(`Expected truthy value, but got ${actual}`);
                    }
                },
                toBeFalsy: () => {
                    if (actual) {
                        throw new Error(`Expected falsy value, but got ${actual}`);
                    }
                },
                toContain: (expected) => {
                    if (!actual.includes(expected)) {
                        throw new Error(`Expected ${actual} to contain ${expected}`);
                    }
                },
                toBeInstanceOf: (expected) => {
                    if (!(actual instanceof expected)) {
                        throw new Error(`Expected instance of ${expected.name}, but got ${actual.constructor.name}`);
                    }
                }
            };
        }
        
        // 测试用例定义

        // 1. 工具函数模块测试 (core/utils/)
        test.describe('工具函数模块 - debug.js', () => {
            test.it('应该提供debug函数', () => {
                expect(typeof window.debug).toBe('function');
                expect(typeof window.getCurrentTime).toBe('function');
                expect(typeof window.sleep).toBe('function');
            });

            test.it('debug函数应该正常工作', () => {
                // 测试debug函数不会抛出错误
                let errorThrown = false;
                try {
                    window.debug('测试消息');
                } catch (e) {
                    errorThrown = true;
                }
                expect(errorThrown).toBeFalsy();
            });

            test.it('getCurrentTime应该返回时间字符串', () => {
                const time = window.getCurrentTime();
                expect(typeof time).toBe('string');
                expect(time.length > 0).toBeTruthy();
            });

            test.it('sleep函数应该返回Promise', () => {
                const promise = window.sleep(10);
                expect(promise).toBeInstanceOf(Promise);
            });
        });

        test.describe('工具函数模块 - dom-utils.js', () => {
            test.it('应该提供DOM操作函数', () => {
                expect(typeof window.findElement).toBe('function');
                expect(typeof window.findElements).toBe('function');
                expect(window.AIPDD.Utils.DOM).toBeTruthy();
            });

            test.it('findElement应该能查找元素', () => {
                const element = window.findElement('body');
                expect(element).toBeTruthy();
                expect(element.tagName.toLowerCase()).toBe('body');
            });

            test.it('createElement应该能创建元素', () => {
                const element = window.AIPDD.Utils.DOM.createElement('div', {className: 'test'}, '测试内容');
                expect(element.tagName.toLowerCase()).toBe('div');
                expect(element.className).toBe('test');
                expect(element.textContent).toBe('测试内容');
            });
        });

        // 2. 设置管理模块测试 (core/settings/)
        test.describe('设置管理模块 - settings-manager.js', () => {
            test.it('应该提供设置管理接口', () => {
                expect(window.AIPDD.Settings).toBeTruthy();
                expect(typeof window.AIPDD.Settings.getSetting).toBe('function');
                expect(typeof window.AIPDD.Settings.updateSetting).toBe('function');
            });

            test.it('应该提供向下兼容的StateManager', () => {
                expect(window.StateManager).toBeTruthy();
                expect(typeof window.StateManager.getState).toBe('function');
                expect(typeof window.StateManager.setState).toBe('function');
            });

            test.it('getSetting应该能获取设置', async () => {
                const setting = await window.AIPDD.Settings.getSetting('testKey', 'defaultValue');
                expect(typeof setting).toBe('string');
            });
        });

        // 3. 存储管理模块测试 (core/storage/)
        test.describe('存储管理模块 - storage-manager.js', () => {
            test.it('应该提供存储管理接口', () => {
                expect(window.AIPDD.Storage).toBeTruthy();
                expect(typeof window.AIPDD.Storage.get).toBe('function');
                expect(typeof window.AIPDD.Storage.set).toBe('function');
            });

            test.it('存储操作应该正常工作', async () => {
                await window.AIPDD.Storage.set('testKey', 'testValue');
                const value = await window.AIPDD.Storage.get('testKey');
                expect(value).toBe('testValue');
            });
        });

        // 4. 转人工功能模块测试 (core/transfer/)
        test.describe('转人工功能模块 - transfer-rules.js', () => {
            test.it('应该提供转人工规则接口', () => {
                expect(window.AIPDD.Transfer.Rules).toBeTruthy();
                expect(typeof window.AIPDD.Transfer.Rules.shouldTransfer).toBe('function');
                expect(typeof window.AIPDD.Transfer.Rules.checkShouldTransfer).toBe('function');
            });

            test.it('shouldTransfer应该能检查转人工条件', async () => {
                const message = { content: '转人工', type: 'text' };
                const result = await window.AIPDD.Transfer.Rules.shouldTransfer(message);
                expect(typeof result).toBe('object');
                expect(typeof result.shouldTransfer).toBe('boolean');
            });
        });

        test.describe('转人工功能模块 - transfer-manager.js', () => {
            test.it('应该提供转人工管理接口', () => {
                expect(window.TransferManager).toBeTruthy();
                expect(typeof window.TransferManager.handleTransfer).toBe('function');
                expect(typeof window.TransferManager.executeTransfer).toBe('function');
            });

            test.it('应该提供向下兼容的全局接口', () => {
                expect(window.AIPDD.Transfer.Manager).toBeTruthy();
                expect(window.TransferManager).toBe(window.AIPDD.Transfer.Manager);
            });
        });

        // 5. UI组件模块测试 (core/ui/)
        test.describe('UI组件模块 - dialog.js', () => {
            test.it('应该提供对话框组件接口', () => {
                expect(window.AIPDD.UI.Dialog).toBeTruthy();
                expect(typeof window.AIPDD.UI.Dialog.createFloatingWindow).toBe('function');
                expect(typeof window.createFloatingWindow).toBe('function');
            });

            test.it('createFloatingWindow应该能创建对话框', () => {
                const dialog = window.createFloatingWindow('测试标题', '测试内容');
                expect(dialog).toBeTruthy();
                expect(dialog.tagName.toLowerCase()).toBe('div');
                expect(dialog.className).toContain('floating-window-overlay');
            });

            test.it('应该提供多种对话框类型', () => {
                expect(typeof window.AIPDD.UI.Dialog.createConfirmDialog).toBe('function');
                expect(typeof window.AIPDD.UI.Dialog.createMessageDialog).toBe('function');
                expect(typeof window.AIPDD.UI.Dialog.createInputDialog).toBe('function');
            });
        });

        // 6. 模块集成测试
        test.describe('模块集成测试', () => {
            test.it('所有核心模块应该正确加载', () => {
                expect(window.AIPDD).toBeTruthy();
                expect(window.AIPDD.Utils).toBeTruthy();
                expect(window.AIPDD.Settings).toBeTruthy();
                expect(window.AIPDD.Storage).toBeTruthy();
                expect(window.AIPDD.Transfer).toBeTruthy();
                expect(window.AIPDD.UI).toBeTruthy();
            });

            test.it('向下兼容接口应该正常工作', () => {
                expect(window.debug).toBeTruthy();
                expect(window.StateManager).toBeTruthy();
                expect(window.TransferManager).toBeTruthy();
                expect(window.createFloatingWindow).toBeTruthy();
            });

            test.it('模块间依赖应该正确建立', () => {
                // 检查设置模块是否能正确使用存储模块
                expect(window.AIPDD.Settings.getSetting).toBeTruthy();
                // 检查转人工模块是否能正确使用设置模块
                expect(window.AIPDD.Transfer.Rules.shouldTransfer).toBeTruthy();
            });
        });

        // 全局测试函数
        window.runAllTests = () => test.runTests();
        window.runCoreTests = () => test.runTests();
        window.clearResults = () => {
            document.getElementById('test-results').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
        };
    </script>
</body>
</html>
